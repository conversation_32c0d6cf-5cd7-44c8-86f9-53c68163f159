/**
    @file
    @ingroup    vms
    @brief      Quectel L76 GPS routines
*/

#include "pelagic.h"
#include "nmea.h"
#include "gps.h"
#include "console.h"
#include "gpio_api.h"

enum {
        GPS_TX_BUFFER_SIZE            = 32,     /**< Bytes for transmit buffer */
        GPS_RX_BUFFER_SIZE            = 128,    /**< Bytes for receive buffer */
};

// turn on only the second sentence (GPRMC)
#define PMTK_SET_NMEA_OUTPUT_RMCONLY "PMTK314,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"
// turn on GPRMC and GGA
#define PMTK_SET_NMEA_OUTPUT_RMCGGA "PMTK314,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"

// turn on RMC, GGA and GSA
#define PMTK_SET_NMEA_OUTPUT_RMCGGAGSA "PMTK314,0,1,0,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0"

// turn on ALL THE DATA
#define PMTK_SET_NMEA_OUTPUT_ALLDATA "PMTK314,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0"
// turn off output
#define PMTK_SET_NMEA_OUTPUT_OFF "PMTK314,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0"

#define PMTK_SET_BACKUP_MODE    "PMTK225,4"

#define PMTK_FULL_MODE          "PMTK225,0"   // Turn on full mode
#define PMTK_ALWAYS_LOCATE_MODE "PMTK225,8"   // Turn on Always Locate standby mode
#define PMTK_PERIODIC_COMMAND "PMTK225,2,%d,%d"

gpio_t gps_en_pin, gps_vbckp_pin, gps_reset_pin, gps_status_pin, gps_pps_pin;
gpio_t gps_status_pin;  // TODO Ian: remove - unused
#ifdef HAVE_V1
gpio_t gps_power_switch;
#endif

bool gps_initted = false;
bool gps_pin_initted = false;

serial_t gps_port;

char gps_tx_buffer[GPS_TX_BUFFER_SIZE];
char gps_rx_buffer[GPS_RX_BUFFER_SIZE];

/** @{ */

#ifdef HAVE_V1
/**
    @brief  apply power to the gps
*/

void
gps_power_on()
{
        // gpio_write(&gps_power_switch, 1);
        // TODO Ian: confirm this sequence is correct
        gpio_write(&gps_vbckp_pin, 1);
        gpio_write(&gps_en_pin, 1);
        osDelay(300);
}

/**
    @brief  cut power to the gps
    */
   
   void
   gps_power_off()
   {
           // gpio_write(&gps_power_switch, 0);
           // TODO Ian: confirm this sequence is correct
        gpio_write(&gps_vbckp_pin, 0);
        gpio_write(&gps_en_pin, 0);

}
#endif

/**
    @brief  initialize the interface to the gps. (does not turn on)
*/

void
gps_init()
{
        if (gps_initted)
                return;

        // hack for now
        gps_port.serial.baudrate=115200; // TODO Ian: remove this hack
                                         // PORT: Also the old documentation says 9600 but the gps onboard is 115200
        serial_init(&gps_port, GNS_TX, GNS_RX);
        serial_baud(&gps_port, 115200);   // TODO Ian: fix serial_baud() to run init again and set rate
        serial_format(&gps_port, 8, ParityNone, 1);
        serial_buffer(&gps_port, gps_tx_buffer, GPS_TX_BUFFER_SIZE, gps_rx_buffer, GPS_RX_BUFFER_SIZE);

        // Try to buffer up until a newline is received before returning data
        serial_line_mode(&gps_port, true);
        serial_power_on(&gps_port);

        gps_pin_setup();
        nmea_init(&gps_loc);

        gps_initted = true;
}

/**
    @brief  factory provision - set up just enough to prevent unnecessary power draws
*/

void
gps_factory_provision()
{
#ifdef HAVE_V1
        gps_pin_setup();
#else
        gps_init();
        gps_reset();
        gps_standby_mode();
        serial_power_off(&gps_port);
#endif
}

/**
    @brief  initialize various GPS related pins
*/

void
gps_pin_setup()
{
        if (gps_pin_initted)
                return;

        //! PORT Ian: init GNS_Vbckp at GPS startup
        gpio_init_out_ex(&gps_vbckp_pin, GNS_Vbckp, 1);
        gpio_init_out(&gps_en_pin, GNS_EN);            //! PORT: supply gps VCC
        gpio_init_out(&gps_reset_pin, GNS_RESET);

        //! PORT: not used
        // gpio_init_in(&gps_status_pin, GPS_STA);
        // gpio_mode(&gps_status_pin, PullDown);

        gpio_init_in(&gps_pps_pin, GNS_PPS);
        gpio_mode(&gps_pps_pin, PullDown);

#ifdef HAVE_V1
        // gpio_init_out_ex(&gps_power_switch, GPS_PWR_SWITCH, 0);
#endif

        gps_pin_initted = true;
}

/**
    @brief  send GPS command and expect acknowledgment
    @param[in]  command string to send without checksum and '$' prefix
    @param[in]  name    command name for failure notification and debugging
    @return true if command was acknowledged and successful.
*/

bool
gps_send_command(const char *command, const char *name)
{
        char  *buffer;
        int bytes;
        char op[4];
        uint8_t checksum = 0;

        // Assumption - command is in format PMTKNNN....
        op[0] = command[4];
        op[1] = command[5];
        op[2] = command[6];
        op[3] = '\0';

        if (gps_show_bytes)
                printf("gps: send[%s] [%s]\n", name, command);

        for (const char *str = command; *str; str++)
                checksum = checksum ^ (uint8_t) *str;

        // flush out any pending data first..
        serial_flush(&gps_port);
        nmea_init(&gps_loc);        // re-init parser

        for (int attempts = 0; attempts < 5; attempts++) {
                uint32_t started = rtc_read();

                bytes = sprintf(gps_data, "$%s*%2.2X\r\n", command, checksum);
                serial_write(&gps_port, gps_data, bytes);

                while ((rtc_read() - started) < 3) {
                        bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);

                        if (bytes == 0)
                                break;                                  // quiet gps?

                        buffer = gps_data;

                        if (gps_show_bytes) {
                                console_write(buffer, bytes);
                                console_flush();
                        }

                        while (bytes-- > 0) {
                                if (nmea_fusedata(&gps_loc, *buffer++) == NMEA_PMTK) {
                                        if (strcmp(gps_loc.words[0], "$PMTK001") == 0 && strcmp(op, gps_loc.words[1]) == 0) {
                                                // command succeeded
                                                if (gps_loc.words[2][0] == '3') {
                                                        return true;
                                                }

                                                EVENT_LOG2(EVT_GPS_COMMAND_ERR, "command failed", "name", EVT_STRCONST, name, "error", EVT_8BIT, gps_loc.words[2][0]);
                                                return false;
                                        }
                                }
                        }
                }
        }

        EVENT_LOG1(EVT_GPS_COMMAND_NOT_CONFIRMED, "command not confirmed", "name", EVT_STRCONST, name);
        return false;
}

/**
    @brief  reset the GPS chip and attempt to initialize
*/

void
gps_reset()
{
        for (int attempts = 0; attempts < 2; attempts++) {
#ifdef TARGET_PLACO
#ifdef HAVE_V1
                gps_power_on();
#endif
                gpio_write(&gps_en_pin, 1); // TODO Ian: resolve gps driver operation
                gpio_write(&gps_reset_pin, 1);
                osDelay(100);
                gpio_write(&gps_reset_pin, 0);
                osDelay(100);
#endif

                if (gps_send_command(PMTK_SET_NMEA_OUTPUT_RMCGGAGSA, "rmc-gga-gsa") == true)
                        return;

                osDelay(1000);
        }
}

/**
    @brief  switch GPS into "low resolution" mode
    @note   The Quectel L76 chip provides an "always locate" mode power savings
            mode. This can dramatically reduce power consumption at the expense of
            accuracy at slower speeds, with sentences only issue when movement is
            detected (in theory).
*/

void
gps_low_res_mode()
{
        for (int attempts = 0; attempts < 2; attempts++) {
#ifdef TARGET_PLACO
                gpio_write(&gps_en_pin, 0);
                osDelay(100);
#endif
                if (gps_send_command(PMTK_ALWAYS_LOCATE_MODE, "always-locate") == true)
                        return;

                if (attempts == 0)
                        gps_reset();
        }
}

/**
    @brief  switch GPS into "high resolution" mode
    @note   This will place the chip into a "full" mode which consumes the most power.
            No power savings occurs, and the chip will issue sentences at a 1hz rate
            (i.e. once per second)
*/

void
gps_highres_mode()
{
        for (int attempts = 0; attempts < 2; attempts++) {
#ifdef TARGET_PLACO
                gpio_write(&gps_en_pin, 1);
                osDelay(10);
#endif
                if (gps_send_command(PMTK_FULL_MODE, "full") == true)
                        return;

                if (attempts == 0)
                        gps_reset();
        }
}

/**
    @brief  switch the GPS into a periodic mode
    @note   The periodic mode is a cycle on/off function mostly used to
            reduce power consumption when the boat has been determined to
            be stationary for a while such as at a dock.
    @param[in]  on  seconds to run the gps for
    @param[in]  off seconds to place the gps into standby mode
*/

void
gps_periodic_mode(uint32_t on, uint32_t off)
{
        for (int attempts = 0; attempts < 2; attempts++) {
                gpio_write(&gps_en_pin, 1);
                osDelay(10);
                sprintf(gps_op, PMTK_PERIODIC_COMMAND, on * 1000, off * 1000);

                if (gps_send_command(gps_op, "periodic") == true)
                        return;

                if (attempts == 0)
                        gps_reset();
        }
}

/**
    @brief  Quick GPS serial communication test - can be called early in main()
    @note   This function tests the basic serial communication with the GPS
            without waiting for the full GPS thread to start. Useful for
            debugging LPUART/serial issues.
    @return true if GPS responds to commands, false if communication fails
*/

bool
gps_test_serial_communication(void)
{
        printf("GPS Serial Test: Starting...\n");

        // Initialize GPS if not already done
        if (!gps_initted) {
                gps_init();
        }

        // Power on GPS and reset
        printf("GPS Serial Test: Powering on and resetting GPS...\n");
        serial_power_on(&gps_port);
        gps_reset();

        // Test 1: Try to send a simple command and get response
        printf("GPS Serial Test: Testing basic command response...\n");
        bool cmd_success = gps_send_command(PMTK_SET_NMEA_OUTPUT_RMCGGAGSA, "test-rmc-gga-gsa");

        if (cmd_success) {
                printf("GPS Serial Test: ✓ Command acknowledged successfully\n");
        } else {
                printf("GPS Serial Test: ✗ Command failed or no response\n");
        }

        // Test 2: Try to read raw data for a few seconds
        printf("GPS Serial Test: Reading raw GPS data for 5 seconds...\n");
        uint32_t start_time = rtc_read();
        int total_bytes = 0;
        int read_attempts = 0;

        while ((rtc_read() - start_time) < 5) {
                int bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);
                read_attempts++;

                if (bytes > 0) {
                        total_bytes += bytes;
                        printf("GPS Serial Test: Read %d bytes (attempt %d)\n", bytes, read_attempts);

                        // Show first few characters for debugging
                        printf("GPS Serial Test: Data sample: ");
                        for (int i = 0; i < bytes && i < 40; i++) {
                                char c = gps_data[i];
                                if (c >= 32 && c <= 126) {
                                        printf("%c", c);
                                } else if (c == '\r') {
                                        printf("\\r");
                                } else if (c == '\n') {
                                        printf("\\n");
                                } else {
                                        printf("\\x%02X", (unsigned char)c);
                                }
                        }
                        printf("\n");
                } else {
                        printf("GPS Serial Test: No data received (attempt %d)\n", read_attempts);
                }
        }

        printf("GPS Serial Test: Total bytes received: %d in %d attempts\n", total_bytes, read_attempts);

        // Test 3: Check if we can parse any NMEA sentences
        printf("GPS Serial Test: Testing NMEA parsing...\n");
        nmea_init(&gps_loc);
        bool found_nmea = false;

        start_time = rtc_read();
        while ((rtc_read() - start_time) < 3 && !found_nmea) {
                int bytes = serial_read_timeout(&gps_port, gps_data, GPS_DATA_SIZE, 1000);

                if (bytes > 0) {
                        for (int i = 0; i < bytes; i++) {
                                nmea_result_t result = nmea_fusedata(&gps_loc, gps_data[i]);
                                if (result != NMEA_NONE && result != NMEA_CRC_ERR) {
                                        printf("GPS Serial Test: ✓ Found valid NMEA sentence type %d\n", result);
                                        found_nmea = true;
                                        break;
                                }
                        }
                }
        }

        if (!found_nmea) {
                printf("GPS Serial Test: ✗ No valid NMEA sentences found\n");
        }

        // Summary
        bool overall_success = cmd_success && (total_bytes > 0);
        printf("GPS Serial Test: %s\n", overall_success ? "✓ PASSED" : "✗ FAILED");
        printf("GPS Serial Test: - Command response: %s\n", cmd_success ? "OK" : "FAIL");
        printf("GPS Serial Test: - Data reception: %s (%d bytes)\n",
               total_bytes > 0 ? "OK" : "FAIL", total_bytes);
        printf("GPS Serial Test: - NMEA parsing: %s\n", found_nmea ? "OK" : "FAIL");

        return overall_success;
}

/**
    @brief  place the gps into standby

    @note
            - For pre-V1 hardware this places the GPS into a standby mode which consumes
              the least power - no satellite tracking is done.
            - For V1, power is simply cut to the GPS.
*/

bool
gps_standby_mode()
{
#ifdef HAVE_V1
        gps_power_off();
        return true;
#else
        for (int attempts = 0; attempts < 2; attempts++) {
                gps_highres_mode();
                osDelay(100);
                gpio_write(&gps_en_pin, 0);

                if (gps_send_command(PMTK_SET_BACKUP_MODE, "backup") == true)
                        return true;

                if (attempts == 0)
                        gps_reset();
        }

        return false;
#endif
}

/** @} */
