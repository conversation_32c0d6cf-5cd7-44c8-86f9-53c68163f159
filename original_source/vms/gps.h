#ifndef __GPS_H__
#define __GPS_H__

enum {
        GPS_PRECISION               = 100000, // 5 points of precision, or 1.1m resolution.

        GPS_DATA_SIZE               = 80,

        GPS_SETTING_UPDATE_RUN_MODE = 0x1,
        GPS_SETTING_UPDATE_IDLE     = 0x2,
        GPS_SETTING_UPDATE_PERIODIC = 0x4,
#if 0
        GPS_SETTING_UPDATE_DOCK     = 0x8,
#endif
};

enum {
        GPS_FLAG_IDLE_ON            = 0x01, // idle the gps when stationary
        GPS_FLAG_SEND_PRECISION     = 0x02, // send satellite and precision information
        GPS_FLAG_SEND_ACCEL_RAW     = 0x04, // send raw accelerometer heading
};

typedef enum {
        GPS_STATE_NORMAL,       // GPS is on - either in low_res, highres or periodic
        GPS_STATE_OFF,          // GPS is off from battery low condition
        GPS_STATE_STATIONARY,   // GPS is on, boat stationary, in periodic updates w/idle settings
#if 0
        GPS_STATE_DOCKED,       // GPS is on, boat in dock, in periodic updates w/dock settings
#endif
} gps_state_t;

typedef enum {
        GPS_TRACK_LOW_RES,      // GPS is on, in low res (lower power)
        GPS_TRACK_HIGH_RES,     // GPS is on in high res / full power mode
        GPS_TRACK_PERIODIC,      // GPS is on, in periodic updates
} gps_track_t;

typedef enum {
        GPS_RUN_MODE_PERIODIC = 0,
        GPS_RUN_MODE_LOW_RES  = 1,
        GPS_RUN_MODE_HIGH_RES = 2,
        GPS_RUN_MODE_DEFAULT  = GPS_RUN_MODE_LOW_RES,
} gps_settings_run_t;

void gps_thread(void const *arg);
void gps_display_location();
void gps_pin_setup();
void gps_settings_init();
void gps_settings_verify();

void gps_init();
void gps_reset();
bool gps_standby_mode();
void gps_highres_mode();
void gps_low_res_mode();
void gps_periodic_mode(uint32_t on, uint32_t off);
void gps_set_high_res_tracking();
void gps_track_appropriately();

// GPS testing functions
bool gps_test_serial_communication(void);

#ifdef HAVE_V1
void gps_power_off();
void gps_power_on();
#endif

extern volatile bool gps_capture_location;
extern volatile bool gps_show_bytes;
extern volatile bool gps_watch_location;

extern volatile bool gps_running;
extern volatile bool gps_have_fix;
extern volatile bool gps_acquired_fix;
extern volatile gps_track_t gps_tracking;
extern volatile uint32_t gps_idle_timestamp;
extern volatile uint32_t gps_fix_time;

extern volatile int32_t gps_lat, gps_lon;
extern volatile double gps_current_lat, gps_current_lon;
extern volatile double gps_current_pdop;
extern volatile uint8_t gps_settings_update;

extern gps_state_t gps_state_requested;
extern volatile gps_state_t gps_state;

extern serial_t gps_port;
extern char gps_op[], gps_data[];
extern nmea_t gps_loc;

#define PDOP_INT()        ((volatile uint32_t)(gps_current_pdop * 10000))

#endif
