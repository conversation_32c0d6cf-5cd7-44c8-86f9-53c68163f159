#include "serial_api_stubs.h"

#include "mbed_assert.h"
#include "pelagic-types.h"
#include "serial_api.h"

#include "cmsis.h"
#include "pinmap.h"
#include "clk_freqs.h"
#include "PeripheralPins.h"

#include "dma.h"

#include "stats.h"
#include "signals.h"
#include "hardware.h"

#include "semihost_api.h"
#include "serial_api_hal.h"
#include "port_stubs.h"
#include "us_ticker_api.h"

int8_t serial_semihost = 0;
serial_t console_uart;

// External references to mbed UART infrastructure
extern UART_HandleTypeDef uart_handlers[];
extern uint32_t serial_irq_ids[];

// void serial_init       (serial_t *obj, PinName tx, PinName rx) {}
// void serial_free       (serial_t *obj) {}
// void serial_baud       (serial_t *obj, int baudrate) {}
// void serial_format     (serial_t *obj, int data_bits, SerialParity parity, int stop_bits) {}

// int  serial_getc       (serial_t *obj) {return 0;}
// void serial_putc       (serial_t *obj, int c) {}
// int  serial_readable   (serial_t *obj) {return 0;}
// int  serial_writable   (serial_t *obj) {return 0;}

int  serial_available  (serial_t *obj)
{
    //! PORT Ian: Check if data is available using HAL flags
    struct serial_s *obj_s = SERIAL_S(obj);
    UART_HandleTypeDef *huart = &uart_handlers[obj_s->index];

    // Check if data is received and ready to read
    return (__HAL_UART_GET_FLAG(huart, UART_FLAG_RXNE) != RESET) ? 1 : 0;
}

int  serial_read       (serial_t *obj, char *buffer, int bytes)
{
    //! PORT Ian: Basic read using serial_read_timeout with infinite timeout
    return serial_read_timeout(obj, buffer, bytes, osWaitForever);
}

int  serial_read_timeout(serial_t *obj, char *buffer, int bytes, uint32_t milliseconds)
{
    //! PORT Ian: Ported from _MODDED_serial_api.c - read with timeout using HAL_UART_Receive
    //! PORT Ian: LPUART instances don't have RTOF flag, so we need different handling
    struct serial_s *obj_s = SERIAL_S(obj);
    UART_HandleTypeDef *huart = &uart_handlers[obj_s->index];

    // Check if this is an LPUART instance (like GPS on LPUART1)
    if (IS_LPUART_INSTANCE(huart->Instance)) {
        //! PORT Ian: LPUART doesn't support hardware timeout and HAL_UART_Receive tries to access RTOF flag
        //! Use low-level polling approach with us_ticker_read() for reliable timeout
        uint32_t start_us = us_ticker_read();
        uint32_t timeout_us = milliseconds * 1000; // Convert ms to us
        int bytes_received = 0;

        while (bytes_received < bytes) {
            // Check for timeout using microsecond ticker (works reliably)
            if ((us_ticker_read() - start_us) >= timeout_us) {
                return bytes_received; // Return partial data received
            }

            // Check if data is available using low-level flag check
            if (__HAL_UART_GET_FLAG(huart, UART_FLAG_RXNE)) {
                // Read the byte directly from the data register
                buffer[bytes_received] = (uint8_t)(huart->Instance->RDR & 0xFF);
                bytes_received++;
            } else {
                // No data available, small delay to avoid busy waiting
                port_delay_ms(1);
            }

            // Check for errors
            if (__HAL_UART_GET_FLAG(huart, UART_FLAG_ORE)) {
                __HAL_UART_CLEAR_OREFLAG(huart);
                // Continue reading despite overrun
            }
        }

        return bytes_received; // All bytes received successfully
    } else {
        //! PORT Ian: Regular UART/USART instances can use HAL_UART_Receive with timeout
        HAL_StatusTypeDef status = HAL_UART_Receive(huart, (uint8_t*)buffer, bytes, milliseconds);

        if (status == HAL_OK) {
            return bytes; // All bytes received
        } else if (status == HAL_TIMEOUT) {
            // Check how many bytes were actually received
            // HAL doesn't provide partial count easily, so return 0 for timeout
            return 0;
        } else {
            // Error occurred
            return 0;
        }
    }
}

int serial_read_signal(serial_t *obj, char *buffer, int bytes, uint16_t *signals)
{
    //! PORT Ian: For now, just call read_timeout_signal with infinite timeout
    return serial_read_timeout_signal(obj, buffer, bytes, osWaitForever, signals);
}

int  serial_read_timeout_signal(serial_t *obj, char *buffer, int bytes, uint32_t milliseconds, uint16_t *signals)
{
    //! PORT Ian: Basic implementation - ignore signals for now, just do timeout read
    if (signals) {
        *signals = 0;
    }

    // For now, just use the basic timeout read
    // TODO: Add proper signal handling later if needed
    return serial_read_timeout(obj, buffer, bytes, milliseconds);
}

void serial_write      (serial_t *obj, char *buffer, int bytes)
{
    //! PORT Ian: Ported from _MODDED_serial_api.c - basic write functionality using HAL
    if (serial_semihost && obj == &console_uart) {
        semihost_write(1, buffer, bytes);
        return;
    }

    // For now, use direct write (no buffering) - can add buffering later
    serial_write_direct(obj, buffer, bytes);
}

void serial_write_direct(serial_t *obj, char *buffer, int bytes)
{
    //! PORT Ian: Ported from _MODDED_serial_api.c - direct write using HAL_UART_Transmit
    struct serial_s *obj_s = SERIAL_S(obj);
    UART_HandleTypeDef *huart = &uart_handlers[obj_s->index];

    // Use HAL blocking transmit with 1 second timeout per byte (reasonable for most cases)
    uint32_t timeout = bytes * 1000; // 1 second per byte
    if (timeout < 1000) timeout = 1000; // Minimum 1 second

    HAL_StatusTypeDef status = HAL_UART_Transmit(huart, (uint8_t*)buffer, bytes, timeout);

    if (status != HAL_OK) {
        // TODO: Could add error handling/stats here
    }
}

void serial_hold       (serial_t *obj)
{
    //! PORT Ian: Stub - hold functionality not implemented yet
    // TODO: Implement buffering and hold/release if needed
    (void)obj;
}

void serial_release    (serial_t *obj)
{
    //! PORT Ian: Stub - release functionality not implemented yet
    // TODO: Implement buffering and hold/release if needed
    (void)obj;
}

void serial_flush      (serial_t *obj)
{
    //! PORT Ian: Clear receive buffer by reading any pending data
    struct serial_s *obj_s = SERIAL_S(obj);
    UART_HandleTypeDef *huart = &uart_handlers[obj_s->index];

    // Clear any pending received data
    while (__HAL_UART_GET_FLAG(huart, UART_FLAG_RXNE) != RESET) {
        volatile uint32_t dummy = huart->Instance->RDR;
        (void)dummy; // Suppress unused variable warning
    }

    // Clear any error flags
    if (__HAL_UART_GET_FLAG(huart, UART_FLAG_ORE)) {
        __HAL_UART_CLEAR_OREFLAG(huart);
    }
}

void serial_buffer     (serial_t *obj, char *tx_buffer, int tx_size, char *rx_buffer, int rx_size)
{
    //! PORT Ian: Stub - buffering not implemented yet, using HAL blocking functions
    // TODO: Implement ring buffer support if needed for performance
    (void)obj;
    (void)tx_buffer;
    (void)tx_size;
    (void)rx_buffer;
    (void)rx_size;
}

void serial_line_mode  (serial_t *obj, int line) 
{
    // See RM0394 pg. 1238, Modbus/ASCII for some details on CR/LF handling
    // USART->CR2 ADD[7:0] is a match character (like \n) 
    // CMF flag must be set for this to trigger an interrupt on character reception

}

void serial_enable_dma_rx(serial_t *obj, char *dma_buffer, int dma_size) 
{
    // See RM0394 pg. 1252 for DMA RX
}

void serial_enable_dma_tx(serial_t *obj) 
{
    // See RM0394 pg. 1251 for DMA TX
}

void serial_suspend_dma_rx(serial_t *obj) 
{

}

void serial_resume_dma_rx(serial_t *obj) 
{

}


void serial_power_off(serial_t *obj)
{
    struct serial_s *obj_s = SERIAL_S(obj);

    serial_free(obj); // does the same thing as RCC section below.

    #ifdef USART_PERIPHERAL_POWERDOWN
    //! Requirements for disabling USART based on RM0394 pg. 1263
    // 1. TE bit must be reset                  USART_CR1_TE
    // 2. USART_ISR TC bit must be set          USART_ISR_TC
    // 3. UE bit must be reset                  USART_CR1_UE
    // --> USART enters low power mode
    //! See RM0394 pg. 1257 for USART behavior in Low Power modes
    // Register content is kept in these low power modes
    //  - Sleep
    //  - LP Run
    //  - Stop 0/1
    //  - Stop 2    -- USART must be disabled or put in reset state
    // Regster contents MUST be reinitialized after
    //  - Standby
    //  - Shutdown
    USART_TypeDef *huart =  ((uint32_t*)(obj->serial.uart) == UART_1) ? USART1 : 
                            ((uint32_t*)(obj->serial.uart) == UART_2) ? USART2 : 
                            ((uint32_t*)(obj->serial.uart) == UART_3) ? USART3 : 
                            ((uint32_t*)(obj->serial.uart) == LPUART_1) ? LPUART1 : NULL;   
    MBED_ASSERT(huart != NULL);
    // Prepare by disabling TX nicely
    LL_USART_DisableDirectionTx(huart);                     // USART_CR1_TE
    while (!READ_BIT(huart->ISR, USART_ISR_TC)) {}
    // Actually disable
    LL_USART_Disable(huart);                                // USART_CR1_UE
    //! Unknowns
    // CR1->UESM    LL_USART_DisableStopMode
    // CR3->UCESM   LL_USART_DisableClockInStopMode
    // CR3->WUFIE   LL_USART_DisableIT_WakeUp               // only when clock is HSI16 or LSE
    #endif

    //! This is what serial_free does
    #ifdef USART_RCC_POWERDOWN
    #if defined(USART1_BASE)
    if (obj_s->uart == UART_1) {
        __HAL_RCC_USART1_FORCE_RESET();
        __HAL_RCC_USART1_RELEASE_RESET();
        __HAL_RCC_USART1_CLK_DISABLE();
    }
    #endif
    #if defined(USART2_BASE)
    if (obj_s->uart == UART_2) {
        __HAL_RCC_USART2_FORCE_RESET();
        __HAL_RCC_USART2_RELEASE_RESET();
        __HAL_RCC_USART2_CLK_DISABLE();
    }
    #endif
    #if defined(USART3_BASE)
    if (obj_s->uart == UART_3) {
        __HAL_RCC_USART3_FORCE_RESET();
        __HAL_RCC_USART3_RELEASE_RESET();
        __HAL_RCC_USART3_CLK_DISABLE();
    }
    #endif
    #if defined(LPUART1_BASE)
    if (obj_s->uart == LPUART_1) {
        __HAL_RCC_LPUART1_FORCE_RESET();
        __HAL_RCC_LPUART1_RELEASE_RESET();
        __HAL_RCC_LPUART1_CLK_DISABLE();
    }
    #endif
    #endif

    // Configure GPIOs back to reset value
    pin_function(obj_s->pin_tx, STM_PIN_DATA(STM_MODE_ANALOG, GPIO_NOPULL, 0));
    pin_function(obj_s->pin_rx, STM_PIN_DATA(STM_MODE_ANALOG, GPIO_NOPULL, 0));

}

// REQUIRES serial_init has been called at least once!
//! OR just don't call this function at all!
void serial_power_on(serial_t *obj) 
{
    struct serial_s *obj_s = SERIAL_S(obj);
    serial_init(obj, obj_s->pin_tx, obj_s->pin_rx);
    // calls _serial_init_direct(serial_t *obj, const serial_pinmap_t *pinmap)
            // struct serial_s *obj_s = SERIAL_S(obj);
                // Get the peripheral name (UART_1, UART_2, ...) from the pin and assign it to the object
            // obj_s->uart = (UARTName)pinmap->peripheral;
            // MBED_ASSERT(obj_s->uart != (UARTName)NC);
                // Reset and enable clock
            // #if defined(USART1_BASE)
            //     if (obj_s->uart == UART_1) {
            //         __HAL_RCC_USART1_CLK_ENABLE();
            //     }
            // #endif
                // ... repeat for all UARTs
                // Assign serial object index
            // obj_s->index = get_uart_index(obj_s->uart);
            // MBED_ASSERT(obj_s->index >= 0); // TODO Ian: this returns 255 which is presumably an error code
                // Configure UART pins
            // pin_function(pinmap->tx_pin, pinmap->tx_function);
            // pin_mode(pinmap->tx_pin, PullUp);
            // pin_function(pinmap->rx_pin, pinmap->rx_function);
            // pin_mode(pinmap->rx_pin, PullUp);
                // Configure UART
            // obj_s->baudrate = 9600;
            // obj_s->databits = UART_WORDLENGTH_8B;
            // obj_s->stopbits = UART_STOPBITS_1;
            // obj_s->parity   = UART_PARITY_NONE;
            // obj_s->pin_tx = pinmap->tx_pin;
            // obj_s->pin_rx = pinmap->rx_pin;
    // calls init_uart(serial_t *obj)
            // struct serial_s *obj_s = SERIAL_S(obj);
            // UART_HandleTypeDef *huart = &uart_handlers[obj_s->index];
            // huart->Instance = (USART_TypeDef *)(obj_s->uart);
            // #if defined(LPUART1_BASE)
            //     if (huart->Instance == LPUART1) {
            //         if (obj_s->baudrate <= 9600) {
            // #if ((MBED_CONF_TARGET_LPUART_CLOCK_SOURCE) & USE_LPUART_CLK_LSE) && defined(USART_CR3_UCESM)
            //             HAL_UARTEx_EnableClockStopMode(huart);
            // #endif
            //             HAL_UARTEx_EnableStopMode(huart);
            //         } else {
            // #if defined(USART_CR3_UCESM)
            //             HAL_UARTEx_DisableClockStopMode(huart);
            // #endif
            //             HAL_UARTEx_DisableStopMode(huart);
            //         }
            //     }
            // #endif
    // calls HAL_UART_Init(UART_HandleTypeDef *huart)
            // /* Check the UART handle allocation */
            // if (huart == NULL) return HAL_ERROR;
            // assert_param((IS_UART_INSTANCE(huart->Instance)) || (IS_LPUART_INSTANCE(huart->Instance)));
            // /* Allocate lock resource and initialize it */
            // if (huart->gState == HAL_UART_STATE_RESET) huart->Lock = HAL_UNLOCKED;
            //     //! ALLEGEDLY Init the low level hardware : GPIO, CLOCK ...
            //     HAL_UART_MspInit(huart); //! actually does nothing!
            // huart->gState = HAL_UART_STATE_BUSY;
            // __HAL_UART_DISABLE(huart);
            // /* Set the UART Communication parameters */
            // UART_SetConfig(huart)
               // In asynchronous mode, the following bits must be kept cleared:
            // CLEAR_BIT(huart->Instance->CR2, (USART_CR2_LINEN | USART_CR2_CLKEN));
            // CLEAR_BIT(huart->Instance->CR3, (USART_CR3_SCEN | USART_CR3_HDSEL | USART_CR3_IREN));
            // __HAL_UART_ENABLE(huart);
            // /* TEACK and/or REACK to check before moving huart->gState and huart->RxState to Ready */
            // return (UART_CheckIdleState(huart));


    //! If the clock was stopped from RCC, are the registers cleared?
    // Probably not. Register contents are retained when in STOP 0/1/2, where all clocks off except LSI/LSE
    // Would have to test to be sure.
    // If not, then power down should be clock gating from RCC
    #ifdef RCC_PERIPHERAL_POWERDOWN
        if (obj_s->uart == UART_1) {
            __HAL_RCC_USART1_CLK_ENABLE();
        }
        if (obj_s->uart == UART_2) {
            __HAL_RCC_USART2_CLK_ENABLE();
        }

        if (obj_s->uart == UART_3) {
            __HAL_RCC_USART3_CLK_ENABLE();
        }
        if (obj_s->uart == LPUART_1) {
            __HAL_RCC_LPUART1_CLK_ENABLE();
        }
    #endif

}


void serial_console_init(bool power_on) 
{
    // pass
}
