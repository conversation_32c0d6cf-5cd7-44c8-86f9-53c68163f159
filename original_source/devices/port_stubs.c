#include "port_stubs.h"
// TODO Ian: implement later
#include "flash_store.h"
#include "wait_api.h"

// See stm32l4xx_hal_conf.h

uint32_t clock_read()
{
    // not identical to rtc_read() in original code
    // returns epoch_clock 
        return 0;
}

void rtc_save()
{
}

void rtc_reset_time()
{
}

// TODO Ian: fix the flash r/w
flash_result_t kinetis_flash_erase_sector(uint32_t address) {
    return FLASH_SUCCESS;
}

flash_result_t kinetis_flash_write(uint32_t address, void *data, int bytes)  {
    return FLASH_SUCCESS;
}

flash_result_t kinetis_flash_read(uint32_t address, void *data, int size) {
    return FLASH_SUCCESS;
}

volatile uint32_t rtc_clock, epoch_clock;


void core_util_critical_section_exit() {}
void core_util_critical_section_enter() {}

uint32_t
bus_frequency(void)
{
    return 16000000; // TODO Ian: bogus value
        // return (OS_CLOCK / (((SIM->CLKDIV1 & SIM_CLKDIV1_OUTDIV4_MASK) >> SIM_CLKDIV1_OUTDIV4_SHIFT) + 1));
}


flash_result_t
stm_flash_erase_sector(uint32_t address)
{
        // bool success;
        // uint8_t we_cmd[] = { CMD_W25_WRITE_ENABLE };
        // // chip rounds down and ignores last bits.
        // uint8_t cmd[] = { CMD_W25_SECTOR_ERASE, (address >> 16) & 0xff, (address >> 8) & 0xff, 0 };

        // if (address & (WINBOND_SECTOR_SIZE - 1))
        //         return FLASH_ERR_ALIGNMENT;

        // winbond_lock();
        // winbond_spi_write(we_cmd, sizeof(we_cmd), NULL, 0);
        // winbond_spi_write(cmd, sizeof(cmd), NULL, 0);

        // success = winbond_wait_ready();
        // winbond_unlock();

        // return success ? FLASH_SUCCESS : FLASH_ERR_RUNTIME;
        return FLASH_SUCCESS;
}

flash_result_t
stm_flash_write(uint32_t address, void *param, int bytes)
{
        // bool success;
        // uint8_t *data = (uint8_t *) param;
        // uint32_t count, remaining;
        // uint8_t we_cmd[] = { CMD_W25_WRITE_ENABLE };
        // uint8_t cmd[4];

        // cmd[0] = CMD_W25_PAGE_PROGRAM;

        // winbond_lock();

        // while (bytes > 0) {
        //         cmd[1] = (address >> 16) & 0xff;
        //         cmd[2] = (address >> 8) & 0xff;
        //         cmd[3] = address & 0xff;

        //         // bytes will wrap around to the top of the page, and not go to
        //         // the next if the count spans pages

        //         remaining = WINBOND_PAGE_SIZE - (address & (WINBOND_PAGE_SIZE - 1));
        //         count = remaining < bytes ? remaining : bytes;

        //         gpio_write(&winbond_wp_pin, 1);
        //         winbond_spi_write(we_cmd, sizeof(we_cmd), NULL, 0);
        //         winbond_spi_write(cmd, sizeof(cmd), data, count);
        //         success = winbond_wait_ready();
        //         gpio_write(&winbond_wp_pin, 0);

        //         if (!success) {
        //                 winbond_unlock();
        //                 return FLASH_ERR_RUNTIME;
        //         }

        //         bytes -= count;
        //         address += count;
        //         data += count;
        // }

        // winbond_unlock();

        return FLASH_SUCCESS;
}

flash_result_t
stm_flash_read(uint32_t address, void *data, int size)
{
        // uint8_t cmd[] = { CMD_W25_FAST_READ_DATA, (address >> 16) & 0xff, (address >> 8) & 0xff, address & 0xff, 0 };

        // // The Winbond chip will continue to pump out bytes until the CS line is released..
        // winbond_lock();
        // winbond_spi_read(cmd, sizeof(cmd), data, size);
        // winbond_unlock();

        return FLASH_SUCCESS;
}

void
stm_flash_keep_on(bool keep_on)
{
        // osMutexWait(winbond_flash_mutex, osWaitForever);
        // winbond_keep_on = keep_on;

        // if (winbond_keep_on == false)
        //         winbond_power_off();

        // osMutexRelease(winbond_flash_mutex);
}

flash_device_t stm_flash_device = {
    .erase            = stm_flash_erase_sector,
    .write            = stm_flash_write,
    .read             = stm_flash_read,
    .keep_on          = stm_flash_keep_on,
    .page_size        = 256,
    .sector_size      = 4096,
    .pages_per_sector = 16
};


// TODO: MBED_ERRORn replaced with return or return UINT32_MAX
// e.g. in mbed_pinmap_common

//! PORT Ian: Reliable delay function that doesn't depend on HAL_GetTick() working
//! Uses existing wait_us() API which works with us_ticker_read() directly
void port_delay_ms(uint32_t milliseconds)
{
    // Use the existing wait_us() function which works reliably
    // wait_us() uses us_ticker_read() directly, not HAL_GetTick()
    wait_us(milliseconds * 1000);
}