QUIET ?= 1
ifeq ($(QUIET),1)
  AD=@
else
  AD=
endif

# Printing
YLW = \033[1;93m
BLU = \033[1;94m
RED = \033[1;91m
RST = \033[0m

RADIO=none

# Somehow the RELEASE set in the top didn't export so just do it here
# RELEASE := $(shell if [ -d tools ]; then tools/release; else ../tools/release; fi)

# gnu make stupid hackery - have to define a space to be within a variable
# to be used as an argument to other gnu make functions
space :=
space +=

BUILD_FLAGS += -DTARGET_$(TARGET)=1

ifeq ($(BNET_VERBOSE),1)
  BUILD_FLAGS += -DBNET_VERBOSE=1
endif

ifeq ($(DEVICE_VERSION),)
	$(error please give a device version on the command line .ex. "make DEVICE_VERSION=10")
else
	ifeq ($(DEVICE_VERSION),01)
		BUILD_FLAGS += -DTARGET_DEVBOARD -DHAVE_LED
		SRC += led.c
		RADIO ?= cc1200
		CLOCK=47972352
	else
		CLOCK=20971520
		BUILD_FLAGS += -DTARGET_PLACO
		ifneq (,$(filter $(DEVICE_VERSION),04 05))
			BUILD_FLAGS += -DHAVE_LED -DHAVE_DIP
			RADIO ?= cc1200
			SRC += led.c dip.c dip_command.c
		else
			ifneq (,$(filter $(DEVICE_VERSION),10 12 125 13))
				RADIO ?= cc1200
				ifeq (,$(filter $(DEVICE_VERSION),13))
					BUILD_FLAGS += -DUSE_OLD_SYNCWORD
				endif
				BUILD_FLAGS += -DHAVE_V1=1
				ifeq ($(PREV1), 1)
					BUILD_FLAGS += -DHAVE_PREV1=1
					PROJOPTS:=$(PROJOPTS)-pre
				endif
			else
				$(error unknown device version $(DEVICE_VERSION))
			endif
		endif
	endif
endif

ifneq ($(RADIO),none)
	SRC += $(RADIO).c
	BUILD_FLAGS += -DHAVE_RADIO=1

	ifneq (,$(filter, $(DEVICE_VERSION),10 12 125 13))
		ifeq ($(RADIO),)
			RADIO=cc1200
		endif
	endif

	ifeq ($(RADIO),cc1200)
		BUILD_FLAGS += -DRADIO_CC1200
	endif
endif

ifeq ($(STACK_USAGE),1)
PROJOPTS += stack-usage
endif

PROJOPTS := $(strip $(PROJOPTS))
ifneq ($(PROJOPTS),)
	PROJOPTS := -$(subst $(space),-,$(PROJOPTS))
endif

# Original... too verbose for this iteration
# PROJECT_TARGET=$(PROJECT)-$(DEVICE_VERSION)-$(RADIO)$(PROJOPTS)-$(RELEASE)
PROJECT_TARGET=pelagic_$(PROJECT)

BUILD_FLAGS += -DDEVICE_VERSION=$(DEVICE_VERSION) -DRADIO=$(RADIO)

BOATOS := $(abspath ..)

GCC_BIN =

VPATH=$(BOATOS)/rtx:$(BOATOS)/cpu:$(BOATOS)/devices:$(BOATOS)/shell:$(BOATOS)/common:$(BOATOS)/bnet:$(BOATOS)/$(PROJECT)

ASM = startup_stm32l431xx.S SVC_Table.S HAL_CM4.S
RTXSRC = HAL_CM.c \
    RTX_Conf_CM.c \
    rt_CMSIS.c \
    rt_Event.c \
    rt_List.c \
    rt_Mailbox.c \
    rt_MemBox.c \
    rt_Memory.c \
    rt_Mutex.c \
    rt_Robin.c \
    rt_Semaphore.c \
    rt_System.c \
    rt_Task.c \
    rt_Time.c \
    system_stm32l4xx.c \
	sleep.c
# port: removed
    # cmsis_nvic.c \


SRC += start.c \
	board_fault.c \
    bits.c \
	printf.c \
	libc.c \
	chip_uid.c \
	alarm.c \
	rtc_api.c \
	semihost_api.c \
	us_ticker.c \
	wait_api.c \
	mcu_sleep.c \
	os_idle.c \
	datetime.c \
	console.c \
	event.c \
	system_file.c \
	uid.c \
	crc16.c \
    announce.c
#removed
# shell.c 
# clock_freq.c \
# us_ticker_api.c \

# common IO support

SRC += 	analogin_api.c \
		analogin_device.c \
		dma.c \
		gpio.c \
		gpio_api.c \
		gpio_irq_api.c \
		gpio_irq_device.c \
		PeripheralPins.c \
		pinmap.c \
		mbed_pinmap_common.c \
		mbed_us_ticker_api.c
# pinmap_common.c

# common device drivers
SRC += 	serial_api.c \
		serial_device.c \
		i2c_api.c

#  TODO Ian: remove and implement where needed
SRC += 	port_stubs.c \
		serial_api_stubs.c \
		serial_wire_debug.c

SRC +=  stm32l4xx_hal.c \
		stm32l4xx_hal_cortex.c \
		stm32l4xx_hal_pwr.c \
		stm32l4xx_hal_pwr_ex.c \
		stm32l4xx_hal_rtc.c \
		stm32l4xx_hal_rtc_ex.c \
		stm32l4xx_hal_rcc.c \
		stm32l4xx_hal_rcc_ex.c \
		stm32l4xx_hal_uart.c \
		stm32l4xx_hal_uart_ex.c \
		stm32l4xx_hal_i2c.c \
		stm32l4xx_hal_i2c_ex.c \
		stm32l4xx_hal_adc.c \
		stm32l4xx_hal_adc_ex.c \

# removed:
# kinetis_flash.c \
# spi_api.c

# ifneq ($(RADIO),none)
# SRC += bnet_common.c
# endif
OBJDIR = $(BOATOS)/objs/$(PROJECT_TARGET)
IMAGEDIR = $(BOATOS)/images
IMAGEBASE = $(IMAGEDIR)/$(PROJECT_TARGET)

OBJRTX = $(RTXSRC:%.c=$(OBJDIR)/%.o)
OBJC = $(SRC:%.c=$(OBJDIR)/%.o)
OBJASM = $(ASM:%.S=$(OBJDIR)/%.o)
OBJS = $(OBJASM) $(OBJRTX) $(OBJC)

DEPENDENCIES = $(OBJS:%.o=%.d)

INCLUDE_PATHS += -I. \
        -I$(BOATOS)/common \
        -I$(BOATOS)/rtx \
        -I$(BOATOS)/cpu \
        -I$(BOATOS)/devices \
        -I$(BOATOS)/devices/hal \
        -I$(BOATOS)/devices/platform \
        -I$(BOATOS)/shell

# ifneq ($(RADIO),none)
# INCLUDE_PATHS += -I$(BOATOS)/bnet
# endif

# PORT Note - the linker script pulls in some macros that need preprocessing
LINKER_SCRIPT_ORIG = $(BOATOS)/cpu/stm32l431xc.ld
LINKER_SCRIPT = $(BOATOS)/cpu/stm32l431xc_pp.ld

AS      = $(GCC_BIN)arm-none-eabi-gcc
CC      = $(GCC_BIN)arm-none-eabi-gcc
CPP     = $(GCC_BIN)arm-none-eabi-g++
LD      = $(GCC_BIN)arm-none-eabi-gcc
OBJCOPY = $(GCC_BIN)arm-none-eabi-objcopy
SIZE  = $(GCC_BIN)arm-none-eabi-size

# TODO Ian: STM_specific defines to enable HAL
# See also original_source/devices/stm32l4xx_hal_conf.h
STM_DEFINES = -DUSE_HAL_DRIVER -DUSE_LL_DRIVER -DSTM32L431xx
MBED_DEFINES = -DDEVICE_SERIAL \
				-DDEVICE_SERIAL_ASYNCH \
				-DDEVICE_I2C \
				-DDEVICE_RTC \
				-DDEVICE_USTICKER \
				-DDEVICE_INTERRUPTIN \
				-DMBED_CONF_TARGET_RTC_CLOCK_SOURCE=USE_RTC_CLK_LSE_OR_LSI \
				-DMBED_CONF_TARGET_LPUART_CLOCK_SOURCE=USE_LPUART_CLK_LSI \
				-DTARGET_STM32L431xC 

# TODO Ian: confirm clock selection in MBED_CONF_TARGET_RTC_CLOCK_SOURCE

# TODO Ian: remove these where possible after exe builds
PORT_FLAGS = -Wno-unused-but-set-variable
WTF_FLAGS = -Wno-address-of-packed-member

CPU = -mcpu=cortex-m4 -mthumb -march=armv7e-m -mfpu=fpv4-sp-d16 -mfloat-abi=hard
AS_FLAGS = -Wa,-adhlns=$(OBJDIR)/$(*F).lst
CC_FLAGS = $(CPU) -c -fno-common -fmessage-length=0 -Wall $(PORT_FLAGS) $(WTF_FLAGS) -fno-exceptions -ffunction-sections -fdata-sections -fno-builtin -MMD -MP
CC_SYMBOLS = -DTOOLCHAIN_GCC -D__CORTEX_M4 -D__FPU_PRESENT=1 -DPELAGIC -D__CMSIS_RTOS -DTARGET_$(BOARD) -DTARGET_$(HARDWARE) $(BUILD_FLAGS) -DOS_CLOCK=$(CLOCK) $(STM_DEFINES) $(MBED_DEFINES)
LD_FLAGS =  $(CPU) -nostartfiles -nostdlib -specs=nano.specs -Wl,--gc-sections -Wl,--use-blx
LD_SYS_LIBS = -lgcc -lnosys -lm

ifeq ($(STACK_USAGE),1)
CC_FLAGS += -fdump-rtl-expand -fstack-usage
else
CC_FLAGS += -Werror
endif

# -O0 for debug
# -Os for size reduction
# #ifdef DEBUG used in a few code fences
DEBUG ?= 1
ifeq ($(DEBUG),1)
  	CC_FLAGS += -g -O0
	CC_SYMBOLS += -DDEBUG
else
  	OPTIMIZATION = -Os
endif


.PHONY: clean rmprovision

ifeq ($(PROVISION), factory)
$(OBJDIR)/provision.o: BUILD_FLAGS += -DFORCE_FACTORY=1
else ifeq ($(PROVISION), shipping)
$(OBJDIR)/provision.o: BUILD_FLAGS += -DFORCE_SHIPPING=1
else ifeq ($(PROVISION), deployed)
$(OBJDIR)/provision.o: BUILD_FLAGS += -DFORCE_DEPLOYED=1
endif

all:
	@echo "Please run make from the top level directory (boatos)."

build: $(IMAGEBASE).bin

flash: $(IMAGEBASE).bin
	sh $(BOATOS)/segger-flash.sh $<

flash-erase: $(IMAGEBASE).bin
	sh $(BOATOS)/segger-flash.sh $< erase

flashbin: $(IMAGEBASE).bin
	openocd -f ../openocd.cfg -c "program $< verify reset"

flashbt: $(IMAGEBASE).bin
	openocd -f ../openocd-bt.cfg -c "program $< verify reset"

flashdev: $(IMAGEBASE).bin
	sudo mount -u -w -o sync /Volumes/MBED
	cp $(IMAGEBASE).bin /Volumes/MBED/$(PROJECT_TARGET).bin

clean:
	rm -f $(IMAGEBASE)*{.bin,.elf,.map}
	rm -rf $(OBJDIR)

$(OBJS): | $(OBJDIR) rmprovision $(LINKER_SCRIPT)

$(OBJDIR):
	mkdir -p $@

$(IMAGEBASE).elf: | $(IMAGEDIR)

$(IMAGEDIR):
	mkdir -p $@

$(OBJDIR)/%.o: %.S
	@echo "Assembling $(subst $(BOATOS)/,,$(abspath $<))"
	$(AD)$(AS) $(CC_FLAGS) $(CC_SYMBOLS) $(CPU) -o $@ $<

$(OBJDIR)/%.o: %.c
	@echo "Compiling $(subst $(BOATOS)/,,$(abspath $<))"
	$(AD)$(CC)  $(CC_FLAGS) $(CC_SYMBOLS) -std=gnu99 $(INCLUDE_PATHS) -o $@ $<

$(IMAGEBASE).elf: $(OBJS)
	@echo "#include <stdint.h>\n const uint32_t build_timestamp = $(BUILDSTAMP); const char build_tag[] = \"$(RELEASE)\";const uint32_t build_target_version=$(DEVICE_VERSION);" > $(OBJDIR)/build_timestamp.c
	$(AD)$(CC)  $(CC_FLAGS) $(CC_SYMBOLS) -std=gnu99 $(INCLUDE_PATHS) -o $(OBJDIR)/build_timestamp.o $(OBJDIR)/build_timestamp.c
	@echo "Linking for board $(TARGET) version $(DEVICE_VERSION) release $(RELEASE) radio $(RADIO) flags $(BUILD_FLAGS)"
	$(AD)$(LD) $(LD_FLAGS) -T$(LINKER_SCRIPT) -Xlinker -Map=$(IMAGEBASE).map -Xlinker --cref -o $@ $^ $(OBJDIR)/build_timestamp.o $(LD_SYS_LIBS) $(LD_SYS_LIBS)
	$(AD)$(SIZE) $@
	$(AD)$(SIZE) $@ | awk 'NR==2 { printf "$(YLW)Final size: %.2f kB$(RST)\n", ($$1 + $$2)/1024 }'
	

$(LINKER_SCRIPT):
	$(AD)$(CC) -E -P -x c $(LINKER_SCRIPT_ORIG) -o $(LINKER_SCRIPT)

$(IMAGEBASE).bin: $(IMAGEBASE).elf
	$(AD)$(OBJCOPY) -O binary $< $@
	@ls -ls $@

# Rebuild provision.o everytime
rmprovision:
	@$(RM) $(OBJDIR)/provision.o

-include $(DEPENDENCIES)
