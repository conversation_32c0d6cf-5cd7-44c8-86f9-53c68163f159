# GPS Serial Test Makefile
# Standalone test for GPS serial communication

# Test-specific settings
TEST_NAME = gps_serial_test
TARGET = $(TEST_NAME)

# Source directories (relative to original_source)
SRCDIR = ../original_source
VMSDIR = $(SRCDIR)/vms
COMMONDIR = $(SRCDIR)/common
DEVICESDIR = $(SRCDIR)/devices
CPUDIR = $(SRCDIR)/cpu
RTXDIR = $(SRCDIR)/rtx
SHELLDIR = $(SRCDIR)/shell

# Output directories
OBJDIR = objs
IMGDIR = images

# Toolchain (same as main project)
GCC_BIN =
AS      = $(GCC_BIN)arm-none-eabi-gcc
CC      = $(GCC_BIN)arm-none-eabi-gcc
CPP     = $(GCC_BIN)arm-none-eabi-g++
LD      = $(GCC_BIN)arm-none-eabi-gcc
OBJCOPY = $(GCC_BIN)arm-none-eabi-objcopy
SIZE    = $(GCC_BIN)arm-none-eabi-size

# Include directories
INCLUDES = -I$(SRCDIR) -I$(VMSDIR) -I$(COMMONDIR) -I$(DEVICESDIR) -I$(CPUDIR) -I$(RTXDIR) -I$(SHELLDIR)

# Test source files
TEST_SOURCES = gps_serial_test.c

# Required VMS sources for GPS functionality
VMS_SOURCES = \
	$(VMSDIR)/gps_quectel.c \
	$(VMSDIR)/nmea.c \
	$(VMSDIR)/settings.c

# Required common sources
COMMON_SOURCES = \
	$(COMMONDIR)/printf.c \
	$(COMMONDIR)/libc.c \
	$(COMMONDIR)/announce.c \
	$(COMMONDIR)/datetime.c \
	$(COMMONDIR)/crc16.c

# Required device sources (minimal set for GPS test)
DEVICE_SOURCES = \
	$(DEVICESDIR)/serial_api.c \
	$(DEVICESDIR)/serial_device.c \
	$(DEVICESDIR)/serial_api_stubs.c \
	$(DEVICESDIR)/gpio_api.c \
	$(DEVICESDIR)/gpio.c \
	$(DEVICESDIR)/pinmap.c \
	$(DEVICESDIR)/mbed_pinmap_common.c \
	$(DEVICESDIR)/PeripheralPins.c \
	$(DEVICESDIR)/wait_api.c \
	$(DEVICESDIR)/us_ticker.c \
	$(DEVICESDIR)/mbed_us_ticker_api.c

# HAL sources
HAL_SOURCES = \
	$(DEVICESDIR)/stm32l4xx_hal.c \
	$(DEVICESDIR)/stm32l4xx_hal_cortex.c \
	$(DEVICESDIR)/stm32l4xx_hal_uart.c \
	$(DEVICESDIR)/stm32l4xx_hal_uart_ex.c \
	$(DEVICESDIR)/stm32l4xx_hal_rtc.c \
	$(DEVICESDIR)/stm32l4xx_hal_rtc_ex.c \
	$(DEVICESDIR)/stm32l4xx_hal_rcc.c \
	$(DEVICESDIR)/stm32l4xx_hal_rcc_ex.c \
	$(DEVICESDIR)/stm32l4xx_hal_pwr.c \
	$(DEVICESDIR)/stm32l4xx_hal_pwr_ex.c \
	$(DEVICESDIR)/stm32l4xx_hal_adc.c \
	$(DEVICESDIR)/stm32l4xx_hal_adc_ex.c

# CPU sources
CPU_SOURCES = \
	$(CPUDIR)/system_stm32l4xx.c

# RTX sources (minimal set)
RTX_SOURCES = \
	$(RTXDIR)/rt_CMSIS.c \
	$(RTXDIR)/rt_System.c \
	$(RTXDIR)/rt_Time.c

# Assembly sources
ASM_SOURCES = \
	$(CPUDIR)/startup_stm32l431xx.S

# All sources
SOURCES = $(TEST_SOURCES) $(VMS_SOURCES) $(COMMON_SOURCES) $(DEVICE_SOURCES) $(HAL_SOURCES) $(CPU_SOURCES) $(RTX_SOURCES)

# Object files
OBJECTS = $(SOURCES:%.c=$(OBJDIR)/%.o) $(ASM_SOURCES:%.S=$(OBJDIR)/%.o)

# Compiler flags (same as main project)
CPU = -mcpu=cortex-m4 -mthumb -march=armv7e-m -mfpu=fpv4-sp-d16 -mfloat-abi=hard

# STM32 and MBED defines (from main project)
STM_DEFINES = -DUSE_HAL_DRIVER -DUSE_LL_DRIVER -DSTM32L431xx
MBED_DEFINES = -DDEVICE_SERIAL \
               -DDEVICE_SERIAL_ASYNCH \
               -DDEVICE_I2C \
               -DDEVICE_RTC \
               -DDEVICE_USTICKER \
               -DDEVICE_INTERRUPTIN \
               -DMBED_CONF_TARGET_RTC_CLOCK_SOURCE=USE_RTC_CLK_LSE_OR_LSI \
               -DMBED_CONF_TARGET_LPUART_CLOCK_SOURCE=USE_LPUART_CLK_LSI \
               -DTARGET_STM32L431xC

# Test-specific defines
TEST_DEFINES = -DHAVE_GPS=1 -DTARGET_VMS=1 -DTARGET_PLACO -DDEVICE_VERSION=12 -DRADIO=none
TEST_DEFINES += -DTOOLCHAIN_GCC -D__CORTEX_M4 -D__FPU_PRESENT=1 -DPELAGIC -D__CMSIS_RTOS
TEST_DEFINES += -DTARGET_VMS -DTARGET_PLACO -DOS_CLOCK=20971520

CFLAGS = $(CPU) -c -fno-common -fmessage-length=0 -Wall -fno-exceptions
CFLAGS += -ffunction-sections -fdata-sections -fno-builtin -MMD -MP
CFLAGS += -g -O0 -DDEBUG
CFLAGS += $(STM_DEFINES) $(MBED_DEFINES) $(TEST_DEFINES)
CFLAGS += $(INCLUDES)

# Linker flags (same as main project)
LDFLAGS = $(CPU) -nostartfiles -nostdlib -specs=nano.specs -Wl,--gc-sections -Wl,--use-blx
LDFLAGS += -T$(SRCDIR)/cpu/stm32l431xc_pp.ld
LD_SYS_LIBS = -lgcc -lnosys -lm

# Default target
all: $(IMGDIR)/$(TARGET).elf $(IMGDIR)/$(TARGET).bin

# Create directories
$(OBJDIR) $(IMGDIR):
	mkdir -p $@

# Compile C sources
$(OBJDIR)/%.o: %.c | $(OBJDIR)
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) -c $< -o $@

# Compile assembly sources
$(OBJDIR)/%.o: %.S | $(OBJDIR)
	@mkdir -p $(dir $@)
	$(CC) $(CFLAGS) -c $< -o $@

# Preprocess linker script (needed for main project compatibility)
$(SRCDIR)/cpu/stm32l431xc_pp.ld: $(SRCDIR)/cpu/stm32l431xc.ld
	$(CC) -E -P -x c $< -o $@

# Link
$(IMGDIR)/$(TARGET).elf: $(OBJECTS) $(SRCDIR)/cpu/stm32l431xc_pp.ld | $(IMGDIR)
	$(LD) $(LDFLAGS) $(OBJECTS) -o $@ $(LD_SYS_LIBS)
	$(SIZE) $@

# Create binary
$(IMGDIR)/$(TARGET).bin: $(IMGDIR)/$(TARGET).elf
	$(OBJCOPY) -O binary $< $@
	@ls -la $@

# Clean
clean:
	rm -rf $(OBJDIR) $(IMGDIR)
	rm -f $(SRCDIR)/cpu/stm32l431xc_pp.ld

# Flash the test (if you have appropriate tools)
flash: $(IMGDIR)/$(TARGET).elf
	$(OPENOCD) -c "set IMAGE $(IMGDIR)/$(TARGET).elf" -f $(OPENOCD_FLASH_SCRIPT) 

.PHONY: all clean flash
