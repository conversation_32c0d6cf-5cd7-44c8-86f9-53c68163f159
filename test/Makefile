# GPS Serial Test Makefile
# Standalone test for GPS serial communication
# Based on original_source/Makefile.common

# Test-specific settings
TEST_NAME = gps_serial_test
TARGET = $(TEST_NAME)

# Source directories (relative to original_source)
BOATOS = ../original_source
SRCDIR = $(BOATOS)
VMSDIR = $(SRCDIR)/vms
COMMONDIR = $(SRCDIR)/common
DEVICESDIR = $(SRCDIR)/devices
CPUDIR = $(SRCDIR)/cpu
RTXDIR = $(SRCDIR)/rtx
SHELLDIR = $(SRCDIR)/shell

# Output directories
OBJDIR = objs
IMGDIR = images

# Toolchain (same as main project)
GCC_BIN =
AS      = $(GCC_BIN)arm-none-eabi-gcc
CC      = $(GCC_BIN)arm-none-eabi-gcc
CPP     = $(GCC_BIN)arm-none-eabi-g++
LD      = $(GCC_BIN)arm-none-eabi-gcc
OBJCOPY = $(GCC_BIN)arm-none-eabi-objcopy
SIZE    = $(GCC_BIN)arm-none-eabi-size

# Tools
MBED=mbed
MBED_2=mbed_tools
GDB=gdb-multiarch
JLINK=JLinkCommander
OPENOCD=openocd

# Scripts
JLINK_FLASH_SCRIPT = ../flash.jlink
JLINK_DEBUG_SCRIPT = ../debug.jlink
JLINK_GDB_FLAGS = -port 2331 -s -device STM32L431CC -endian little -speed 4000 -if swd -vd
OPENOCD_FLASH_SCRIPT = ../flash.cfg
OPENOCD_DEBUG_SCRIPT = ../debug.cfg
GDB_INIT_SCRIPT = ../connect.gdb
GDB_UTILS_SCRIPT = ../utils.gdb

# Build Flags
QUIET ?= 1

# Include paths (same as main project)
INCLUDE_PATHS = -I. \
        -I$(BOATOS)/common \
        -I$(BOATOS)/rtx \
        -I$(BOATOS)/cpu \
        -I$(BOATOS)/devices \
        -I$(BOATOS)/devices/hal \
        -I$(BOATOS)/devices/platform \
        -I$(BOATOS)/shell \
        -I$(BOATOS)/vms

# Assembly sources (from main project)
ASM = startup_stm32l431xx.S SVC_Table.S HAL_CM4.S

# RTX sources (from main project)
RTXSRC = HAL_CM.c \
    RTX_Conf_CM.c \
    rt_CMSIS.c \
    rt_Event.c \
    rt_List.c \
    rt_Mailbox.c \
    rt_MemBox.c \
    rt_Memory.c \
    rt_Mutex.c \
    rt_Robin.c \
    rt_Semaphore.c \
    rt_System.c \
    rt_Task.c \
    rt_Time.c \
    system_stm32l4xx.c \
    sleep.c

# Common sources (subset from main project)
SRC = start.c \
	board_fault.c \
    bits.c \
	printf.c \
	libc.c \
	chip_uid.c \
	alarm.c \
	rtc_api.c \
	semihost_api.c \
	us_ticker.c \
	wait_api.c \
	mcu_sleep.c \
	os_idle.c \
	datetime.c \
	console.c \
	event.c \
	system_file.c \
	uid.c \
	crc16.c \
    announce.c \
	analogin_api.c \
	analogin_device.c \
	dma.c \
	gpio.c \
	gpio_api.c \
	gpio_irq_api.c \
	gpio_irq_device.c \
	PeripheralPins.c \
	pinmap.c \
	mbed_pinmap_common.c \
	mbed_us_ticker_api.c \
	serial_api.c \
	serial_device.c \
	i2c_api.c \
	port_stubs.c \
	serial_api_stubs.c \
	serial_wire_debug.c \
	stm32l4xx_hal.c \
	stm32l4xx_hal_cortex.c \
	stm32l4xx_hal_pwr.c \
	stm32l4xx_hal_pwr_ex.c \
	stm32l4xx_hal_rtc.c \
	stm32l4xx_hal_rtc_ex.c \
	stm32l4xx_hal_rcc.c \
	stm32l4xx_hal_rcc_ex.c \
	stm32l4xx_hal_uart.c \
	stm32l4xx_hal_uart_ex.c \
	stm32l4xx_hal_i2c.c \
	stm32l4xx_hal_i2c_ex.c \
	stm32l4xx_hal_adc.c \
	stm32l4xx_hal_adc_ex.c

# VMS sources for GPS test
VMS_SRC = gps_quectel.c \
	nmea.c \
	settings.c

# Test source
TEST_SRC = gps_serial_test.c

# Object files (same structure as main project)
OBJRTX = $(RTXSRC:%.c=$(OBJDIR)/%.o)
OBJC = $(SRC:%.c=$(OBJDIR)/%.o)
OBJVMS = $(VMS_SRC:%.c=$(OBJDIR)/%.o)
OBJTEST = $(TEST_SRC:%.c=$(OBJDIR)/%.o)
OBJASM = $(ASM:%.S=$(OBJDIR)/%.o)
OBJS = $(OBJASM) $(OBJRTX) $(OBJC) $(OBJVMS) $(OBJTEST)

# Compiler settings (same as main project)
CPU = -mcpu=cortex-m4 -mthumb -march=armv7e-m -mfpu=fpv4-sp-d16 -mfloat-abi=hard

# STM32 and MBED defines (from main project)
STM_DEFINES = -DUSE_HAL_DRIVER -DUSE_LL_DRIVER -DSTM32L431xx
MBED_DEFINES = -DDEVICE_SERIAL \
				-DDEVICE_SERIAL_ASYNCH \
				-DDEVICE_I2C \
				-DDEVICE_RTC \
				-DDEVICE_USTICKER \
				-DDEVICE_INTERRUPTIN \
				-DMBED_CONF_TARGET_RTC_CLOCK_SOURCE=USE_RTC_CLK_LSE_OR_LSI \
				-DMBED_CONF_TARGET_LPUART_CLOCK_SOURCE=USE_LPUART_CLK_LSI \
				-DTARGET_STM32L431xC

# Build flags (from main project)
BUILD_FLAGS = -DHAVE_GPS=1 -DTARGET_VMS=1 -DTARGET_PLACO -DDEVICE_VERSION=12 -DRADIO=none -DHAVE_V1=1 -DUSE_OLD_SYNCWORD

# Port flags (from main project)
PORT_FLAGS = -Wno-unused-but-set-variable
WTF_FLAGS = -Wno-address-of-packed-member

# Compiler flags (same as main project)
CC_FLAGS = $(CPU) -c -fno-common -fmessage-length=0 -Wall $(PORT_FLAGS) $(WTF_FLAGS) -fno-exceptions -ffunction-sections -fdata-sections -fno-builtin -MMD -MP
CC_SYMBOLS = -DTOOLCHAIN_GCC -D__CORTEX_M4 -D__FPU_PRESENT=1 -DPELAGIC -D__CMSIS_RTOS -DTARGET_VMS -DTARGET_PLACO $(BUILD_FLAGS) -DOS_CLOCK=20971520 $(STM_DEFINES) $(MBED_DEFINES)

# Debug settings
DEBUG = 1
ifeq ($(DEBUG),1)
	CC_FLAGS += -g -O0
	CC_SYMBOLS += -DDEBUG
else
	CC_FLAGS += -Os
endif

# Linker flags (same as main project)
LD_FLAGS = $(CPU) -nostartfiles -nostdlib -specs=nano.specs -Wl,--gc-sections -Wl,--use-blx
LD_SYS_LIBS = -lgcc -lnosys -lm

# Linker script
LINKER_SCRIPT_ORIG = $(BOATOS)/cpu/stm32l431xc.ld
LINKER_SCRIPT = $(BOATOS)/cpu/stm32l431xc_pp.ld

# VPATH for source files (same as main project)
VPATH=$(BOATOS)/rtx:$(BOATOS)/cpu:$(BOATOS)/devices:$(BOATOS)/shell:$(BOATOS)/common:$(BOATOS)/vms:.

# Default target
all: $(IMGDIR)/$(TARGET).elf $(IMGDIR)/$(TARGET).bin

# Create directories
$(OBJDIR) $(IMGDIR):
	mkdir -p $@

# Compile assembly sources (same as main project)
$(OBJDIR)/%.o: %.S | $(OBJDIR)
	@echo "Assembling $<"
	@mkdir -p $(dir $@)
	$(AS) $(CC_FLAGS) $(CC_SYMBOLS) $(CPU) -o $@ $<

# Compile C sources (same as main project)
$(OBJDIR)/%.o: %.c | $(OBJDIR)
	@echo "Compiling $<"
	@mkdir -p $(dir $@)
	$(CC) $(CC_FLAGS) $(CC_SYMBOLS) -std=gnu99 $(INCLUDE_PATHS) -o $@ $<

# Preprocess linker script (same as main project)
$(LINKER_SCRIPT): $(LINKER_SCRIPT_ORIG)
	$(CC) -E -P -x c $< -o $@

# Link (same as main project)
$(IMGDIR)/$(TARGET).elf: $(OBJS) $(LINKER_SCRIPT) | $(IMGDIR)
	@echo "Linking GPS test"
	$(LD) $(LD_FLAGS) -T$(LINKER_SCRIPT) -Xlinker -Map=$(IMGDIR)/$(TARGET).map -Xlinker --cref -o $@ $(OBJS) $(LD_SYS_LIBS)
	$(SIZE) $@

# Create binary
$(IMGDIR)/$(TARGET).bin: $(IMGDIR)/$(TARGET).elf
	$(OBJCOPY) -O binary $< $@
	@ls -la $@

# Clean
clean:
	rm -rf $(OBJDIR) $(IMGDIR)
	rm -f $(LINKER_SCRIPT)

# Flash the test (if you have appropriate tools)
flash: $(IMGDIR)/$(TARGET).elf
	$(OPENOCD) -c "set IMAGE $(IMGDIR)/$(TARGET).elf" -f $(OPENOCD_FLASH_SCRIPT)

.PHONY: all clean flash
